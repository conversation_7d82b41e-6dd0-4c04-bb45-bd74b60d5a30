#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::Window;
use cpal::traits::{<PERSON><PERSON>Trai<PERSON>, HostTrait, StreamTrait};
use hound::{WavWriter, WavSpec};
use std::sync::{Arc, Mutex};
use std::fs;

// Learn more about Tauri commands at https://tauri.app/v1/guides/features/command
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
async fn record_audio(_window: Window, file_name: String) -> Result<String, String> {
    let host = cpal::default_host();
    let device = host.default_input_device().ok_or("No input device found")?;

    let config = device.default_input_config().map_err(|e| e.to_string())?;
    println!("Default input config: {:?}", config);

    // Get the user's home directory and create recordings folder
    let home_dir = std::env::var("HOME").map_err(|_| "Could not find home directory")?;
    let recordings_dir = std::path::Path::new(&home_dir).join("Documents").join("recordings");

    // Ensure the recordings directory exists
    fs::create_dir_all(&recordings_dir).map_err(|e| e.to_string())?;

    let file_path = recordings_dir.join(file_name);

    let spec = WavSpec {
        channels: config.channels() as _,
        sample_rate: config.sample_rate().0 as _,
        bits_per_sample: 16,
        sample_format: hound::SampleFormat::Int,
    };

    let writer = WavWriter::create(&file_path, spec).map_err(|e| e.to_string())?;
    let writer = Arc::new(Mutex::new(Some(writer)));

    let writer_clone = writer.clone();
    let stream = device.build_input_stream(
        &config.into(),
        move |data: &[f32], _: &cpal::InputCallbackInfo| {
            if let Some(writer) = writer_clone.lock().unwrap().as_mut() {
                for &sample in data {
                    let sample_i16 = (sample * i16::MAX as f32) as i16;
                    writer.write_sample(sample_i16).unwrap();
                }
            }
        },
        move |err| {
            eprintln!("An error occurred on stream: {}", err);
        },
        None,
    ).map_err(|e| e.to_string())?;

    stream.play().map_err(|e| e.to_string())?;

    // For demonstration, record for 5 seconds
    std::thread::sleep(std::time::Duration::from_secs(5));

    drop(stream); // Stop the stream
    writer.lock().unwrap().take().unwrap().finalize().map_err(|e| e.to_string())?;

    Ok(format!("Audio recorded to {}", file_path.display()))
}


fn main() {
    tauri::Builder::default()
        .plugin(tauri_plugin_fs::init()) // Initialize the plugin
        .invoke_handler(tauri::generate_handler![greet, record_audio])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
